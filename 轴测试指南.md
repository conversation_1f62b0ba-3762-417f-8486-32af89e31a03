# MLX90393 轴测试指南

## 🎯 **目标：确定哪个轴在响应**

既然INT可以响应，现在需要确定是哪个轴触发的中断。

## 🔧 **轴测试方法**

### **步骤1: 查看当前配置**
运行程序后会显示：
```
WOC Config: X Y Z axes, Threshold=500 LSB
```

### **步骤2: 分析中断输出**
当INT触发时，会显示：
```
*** MAGNETIC FIELD CHANGE DETECTED! ***
X:  25.3 Y: 285.6 Z:156.7 [WOC]
Changes: dX=12.5 dY=3.2 dZ=45.8
Primary axis: Z-axis
```

## 🧪 **单轴测试**

### **测试X轴** (在mlx90393.h中修改)
```c
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_X  // 只测试X轴
```
**测试方法**: 磁铁左右移动 (水平方向)

### **测试Y轴**
```c
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Y  // 只测试Y轴
```
**测试方法**: 磁铁前后移动 (水平方向)

### **测试Z轴**
```c
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z  // 只测试Z轴
```
**测试方法**: 磁铁上下移动 (垂直方向)

## 📊 **测试记录表**

| 轴配置 | INT响应 | 磁铁方向 | 备注 |
|--------|---------|----------|------|
| 只X轴  | ✓/✗    | 左右移动 |      |
| 只Y轴  | ✓/✗    | 前后移动 |      |
| 只Z轴  | ✓/✗    | 上下移动 |      |

## 🎯 **快速测试步骤**

### **1. 测试Z轴** (最常用)
```c
// 在 Inc/mlx90393.h 中修改
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z
```
- 编译下载
- 用磁铁垂直靠近/远离传感器
- 观察INT引脚和串口输出

### **2. 测试X轴**
```c
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_X
```
- 编译下载
- 用磁铁水平左右移动
- 观察响应

### **3. 测试Y轴**
```c
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Y
```
- 编译下载
- 用磁铁水平前后移动
- 观察响应

## 🔍 **预期结果分析**

### **如果Z轴响应最好**
```
WOC Config: Z axes, Threshold=500 LSB
*** MAGNETIC FIELD CHANGE DETECTED! ***
X:  25.3 Y: 285.6 Z:156.7 [WOC]
Changes: dX=1.2 dY=2.1 dZ=45.8
Primary axis: Z-axis
```
**说明**: Z轴最敏感，适合垂直方向检测

### **如果X或Y轴响应好**
```
WOC Config: X axes, Threshold=500 LSB
*** MAGNETIC FIELD CHANGE DETECTED! ***
X:  25.3 Y: 285.6 Z:156.7 [WOC]
Changes: dX=35.5 dY=1.2 dZ=2.8
Primary axis: X-axis
```
**说明**: X轴敏感，适合水平方向检测

## 🛠️ **优化建议**

### **找到最敏感的轴后**
1. **单轴配置**: 只监测最敏感的轴
2. **降低阈值**: 提高灵敏度
3. **应用优化**: 根据轴特性设计应用

### **示例：如果Z轴最好**
```c
// 优化配置
#define MLX90393_WOC_AXES           MLX90393_WOC_AXIS_Z     // 只监测Z轴
#define MLX90393_WOC_SENSITIVITY_MEDIUM                     // 提高灵敏度
```

## 🎯 **应用场景匹配**

### **Z轴敏感** → 适合：
- 门窗开关检测 (磁铁垂直移动)
- 物体接近检测 (垂直方向)
- 盖子开关检测

### **X/Y轴敏感** → 适合：
- 滑动门检测 (水平移动)
- 旋转检测
- 水平位移监测

## 📋 **测试清单**

- [ ] 测试Z轴单独响应
- [ ] 测试X轴单独响应  
- [ ] 测试Y轴单独响应
- [ ] 记录最敏感的轴
- [ ] 优化配置 (单轴+高灵敏度)
- [ ] 验证优化后的性能

## 🚀 **下一步**

1. **确定最佳轴**: 通过单轴测试找出最敏感的轴
2. **优化配置**: 配置为单轴监测
3. **提高灵敏度**: 改为MEDIUM或HIGH
4. **实际应用**: 根据轴特性设计具体应用

通过这个测试，你就能确定MLX90393的哪个轴最适合你的应用场景了！
