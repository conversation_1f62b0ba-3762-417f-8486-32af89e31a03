# MLX90393 WOC模式使用说明

## 🎯 功能概述

WOC (Wake-up On Change) 模式是MLX90393的一个重要功能，专门用于**低功耗磁场监测**应用。

### 🔋 应用场景
- **门窗监测**: 检测门窗开关状态
- **位置检测**: 监测物体位置变化  
- **安防系统**: 磁场异常报警
- **低功耗IoT**: 电池供电的长期监测

## ⚙️ 工作原理

### 1. **配置阶段**
```c
// 配置WOC模式
MLX90393_ConfigureWOC(&mlx_handle);
```
- 设置TRIG_INT = 1 (INT模式)
- 设置WOC_DIFF = 1 (启用变化检测)
- 配置X/Y/Z轴变化阈值

### 2. **启动阶段**
```c
// 启动WOC模式
MLX90393_StartWOC(&mlx_handle);
```
- 发送WOC命令 (0xE0)
- 指定监测轴 (X+Y+Z)
- MLX90393进入WOC模式

### 3. **监测阶段**
- MLX90393持续监测磁场变化
- 当变化超过阈值时，INT引脚输出中断
- 单片机从休眠状态被唤醒

### 4. **处理阶段**
```c
// 检查中断原因
MLX90393_CheckWOCStatus(&mlx_handle);
```
- 读取当前磁场数据
- 处理中断事件
- 可选择继续WOC模式或退出

## 📋 配置参数

### 阈值设置 (在mlx90393.h中)
```c
#define MLX90393_WOC_XY_THRESHOLD   100     // X/Y轴变化阈值 (LSB)
#define MLX90393_WOC_Z_THRESHOLD    100     // Z轴变化阈值 (LSB)  
#define MLX90393_WOC_T_THRESHOLD    50      // 温度变化阈值 (LSB)
#define MLX90393_WOC_AXES           0x0E    // 监测轴选择 (X+Y+Z)
```

### 阈值调整建议
- **高灵敏度**: 阈值 = 50-100 LSB
- **中等灵敏度**: 阈值 = 100-200 LSB  
- **低灵敏度**: 阈值 = 200-500 LSB

## 🔌 硬件连接

### INT引脚连接
```
MLX90393 INT引脚 → STM32 GPIO (配置为外部中断)
```

### 中断配置示例
```c
// 在CubeMX中配置GPIO为外部中断
// 触发方式: 下降沿触发
// 上拉电阻: 启用

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin) {
    if (GPIO_Pin == MLX_INT_Pin) {
        // WOC中断处理
        MLX90393_CheckWOCStatus(&mlx_handle);
    }
}
```

## 💡 使用示例

### 完整的低功耗应用流程
```c
void LowPowerMagneticMonitor(void) {
    // 1. 初始化传感器
    MLX90393_Init(&mlx_handle, &hi2c2);
    
    // 2. 配置WOC模式
    MLX90393_ConfigureWOC(&mlx_handle);
    
    // 3. 启动WOC模式
    MLX90393_StartWOC(&mlx_handle);
    
    // 4. 进入主循环
    while (1) {
        // 单片机进入休眠模式
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        
        // 被INT中断唤醒后执行
        printf("磁场变化检测到!\r\n");
        
        // 处理中断
        MLX90393_CheckWOCStatus(&mlx_handle);
        
        // 可选: 重新启动WOC模式继续监测
        // MLX90393_StartWOC(&mlx_handle);
    }
}
```

## 🚨 注意事项

### 1. **中断处理**
- INT引脚在检测到变化时会输出低电平
- 需要配置为下降沿触发中断
- 建议启用内部上拉电阻

### 2. **功耗优化**
- WOC模式下MLX90393功耗约10-20μA
- 单片机休眠功耗可降至μA级别
- 总系统功耗可实现超低功耗运行

### 3. **阈值调整**
- 根据应用场景调整阈值
- 阈值过小可能导致误触发
- 阈值过大可能错过重要变化

### 4. **环境因素**
- 温度变化可能影响磁场读数
- 建议启用温度补偿
- 考虑环境磁场干扰

## 🔧 调试技巧

### 1. **验证配置**
```c
// 读取配置寄存器确认设置
MLX90393_ReadConfiguration(&mlx_handle);
```

### 2. **测试中断**
- 用磁铁靠近传感器测试
- 观察INT引脚电平变化
- 检查中断服务程序是否被调用

### 3. **监测状态**
```c
// 定期检查WOC状态
uint8_t status = MLX90393_CheckWOCStatus(&mlx_handle);
```

## 📊 性能指标

- **响应时间**: < 10ms
- **功耗**: 10-20μA (WOC模式)
- **检测精度**: 可配置 (50-500 LSB)
- **工作温度**: -40°C 到 +85°C

## 🎉 总结

WOC模式是实现低功耗磁场监测的理想选择，特别适合电池供电的IoT应用。通过合理配置阈值和中断处理，可以实现高效、可靠的磁场变化检测系统。
