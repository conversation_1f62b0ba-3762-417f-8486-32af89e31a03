# MLX90393 WOC配置参数说明

## 🔧 可调整的WOC参数 (在mlx90393.h中)

### 1. **WOC功能开关**
```c
#define MLX90393_WOC_ENABLE         1       // 1=启用WOC, 0=禁用WOC
```

### 2. **灵敏度预设** (选择其中一个)
```c
// 高灵敏度 (容易触发)
#define MLX90393_WOC_SENSITIVITY_HIGH    

// 中等灵敏度 (推荐)
#define MLX90393_WOC_SENSITIVITY_MEDIUM  

// 低灵敏度 (不易触发) - 当前选择
#define MLX90393_WOC_SENSITIVITY_LOW     
```

### 3. **自定义阈值** (如果不使用预设)
```c
#define MLX90393_WOC_XY_THRESHOLD   500     // X/Y轴阈值 (50-1000 LSB)
#define MLX90393_WOC_Z_THRESHOLD    500     // Z轴阈值 (50-1000 LSB)
#define MLX90393_WOC_T_THRESHOLD    100     // 温度阈值 (LSB)
```

### 4. **监测轴选择** (可组合)
```c
// 单独轴选择
#define MLX90393_WOC_AXIS_X         0x08    // 监测X轴
#define MLX90393_WOC_AXIS_Y         0x04    // 监测Y轴  
#define MLX90393_WOC_AXIS_Z         0x02    // 监测Z轴
#define MLX90393_WOC_AXIS_T         0x01    // 监测温度

// 组合选择 (当前配置: X+Y+Z)
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)
```

## 📊 灵敏度对照表

| 灵敏度级别 | X/Y阈值 | Z阈值 | 适用场景 |
|-----------|---------|-------|----------|
| HIGH      | 50 LSB  | 50 LSB | 微小变化检测 |
| MEDIUM    | 200 LSB | 200 LSB | 一般应用 |
| LOW       | 500 LSB | 500 LSB | 避免误触发 |

## 🎯 常用配置示例

### 1. **门窗监测** (高灵敏度)
```c
#define MLX90393_WOC_SENSITIVITY_HIGH
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y)
```

### 2. **位置检测** (中等灵敏度)
```c
#define MLX90393_WOC_SENSITIVITY_MEDIUM
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)
```

### 3. **安防报警** (低灵敏度，避免误报)
```c
#define MLX90393_WOC_SENSITIVITY_LOW
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_Z)  // 只监测Z轴
```

### 4. **自定义配置**
```c
// 注释掉所有预设
// #define MLX90393_WOC_SENSITIVITY_HIGH
// #define MLX90393_WOC_SENSITIVITY_MEDIUM  
// #define MLX90393_WOC_SENSITIVITY_LOW

// 自定义阈值
#define MLX90393_WOC_XY_THRESHOLD   150     // 自定义X/Y阈值
#define MLX90393_WOC_Z_THRESHOLD    300     // 自定义Z轴阈值
```

## ⚡ 程序行为

### **WOC启用时** (`MLX90393_WOC_ENABLE = 1`)
```
启动时输出:
Configuring WOC mode...
WOC mode configured. INT pin ready for magnetic field monitoring.
Thresholds: X/Y=500, Z=500 LSB

正常测量输出:
X:  -0.3 Y: 263.6 Z:113.7
X:   5.7 Y: 267.4 Z:148.8

WOC中断检测时:
*** MAGNETIC FIELD CHANGE DETECTED! ***
X:  25.3 Y: 285.6 Z:156.7 [WOC]
```

### **WOC禁用时** (`MLX90393_WOC_ENABLE = 0`)
```
只有正常测量输出:
X:  -0.3 Y: 263.6 Z:113.7
X:   5.7 Y: 267.4 Z:148.8
```

## 🔌 INT引脚行为

### **正常工作状态**:
- **初始**: 低电平 (0V)
- **检测到变化**: 高电平 (3.3V)
- **读取数据后**: 自动返回低电平

### **LED指示**:
- **LED2**: 正常测量指示 (每20次测量闪烁)
- **LED3**: WOC中断指示 (检测到磁场变化时闪烁)

## 🛠️ 调试技巧

### 1. **测试不同灵敏度**
修改灵敏度预设，重新编译测试:
```c
// 从低灵敏度开始测试
#define MLX90393_WOC_SENSITIVITY_LOW

// 如果不够敏感，改为中等
#define MLX90393_WOC_SENSITIVITY_MEDIUM

// 如果还不够，改为高灵敏度
#define MLX90393_WOC_SENSITIVITY_HIGH
```

### 2. **测试不同轴组合**
```c
// 只测试Z轴 (垂直方向)
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z

// 只测试X+Y轴 (水平方向)
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y)

// 测试所有轴
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)
```

### 3. **观察输出**
- 正常测量: `X:  -0.3 Y: 263.6 Z:113.7`
- WOC中断: `*** MAGNETIC FIELD CHANGE DETECTED! ***`
- 中断数据: `X:  25.3 Y: 285.6 Z:156.7 [WOC]`

## 📋 快速配置指南

1. **打开 `Inc/mlx90393.h`**
2. **选择灵敏度**: 注释/取消注释对应的 `SENSITIVITY` 定义
3. **选择监测轴**: 修改 `MLX90393_WOC_AXES` 定义
4. **编译下载测试**
5. **用磁铁测试INT功能**

## ✅ 验证方法

1. **启动时看到**: `WOC mode configured. INT pin ready...`
2. **用磁铁靠近传感器**
3. **观察输出**: `*** MAGNETIC FIELD CHANGE DETECTED! ***`
4. **检查INT引脚**: 从低电平变为高电平
5. **LED3闪烁**: 表示WOC中断工作正常

通过调整这些参数，可以根据具体应用需求优化WOC功能的性能。
