# MLX90393 WOC模式调试指南

## 🚨 当前问题分析

### 观察到的现象：
- **INT引脚输出15Hz方波** → 说明处于连续测量模式
- **WOC命令发送失败** → `Failed to send WOC command (HAL: 1)`
- **寄存器配置成功** → `Register 0x01 configured: 0x8F40`

### 问题根因：
1. **WOC命令码错误** - 已修正从0xE0改为0x20
2. **命令格式可能不正确** - 需要验证轴选择参数
3. **TRIG_INT配置可能未生效** - 需要验证寄存器写入

## 🔧 修复措施

### 1. 命令码修正
```c
// 修正前 (错误)
#define MLX90393_CMD_WOC  0xE0

// 修正后 (正确)  
#define MLX90393_CMD_WOC  0x20
```

### 2. 增强调试功能
- 添加寄存器读写验证
- 增加详细的状态位解析
- 提供简化的WOC测试函数

### 3. 新的测试流程
```c
MLX90393_SimpleWOCTest(&mlx_handle);
```

## 📋 调试步骤

### 步骤1: 验证I2C通信
```
✅ Device found at address 0x18 (7-bit: 0x0C)
✅ Communication test PASSED
```

### 步骤2: 检查寄存器配置
运行新代码后观察输出：
```
Current register 0x01: 0x????
Setting register 0x01 to: 0x????
Register 0x01 verified: 0x????
```

**期望值分析:**
- TRIG_INT (bit 9) = 1 → INT模式
- WOC_DIFF (bit 6) = 1 → 启用变化检测

### 步骤3: 测试WOC命令
观察输出：
```
Trying WOC command 0x20 with axes 0x0E...
WOC command successful! Status: 0x??
```

### 步骤4: 监测INT引脚
- **正常状态**: 高电平 (3.3V)
- **中断触发**: 低电平 (0V)
- **不应该**: 持续方波

## 🔍 可能的问题和解决方案

### 问题1: 寄存器写入失败
**症状**: 验证读取的值与写入的值不同
**解决**: 
- 检查写寄存器命令格式
- 确认寄存器地址正确
- 验证I2C时序

### 问题2: WOC命令格式错误
**症状**: HAL_I2C_Master_Transmit返回HAL_ERROR
**解决**:
- 尝试不同的命令格式
- 检查轴选择参数
- 验证命令序列

### 问题3: INT引脚硬件配置
**症状**: INT引脚始终输出方波
**解决**:
- 检查TRIG_INT位是否正确设置
- 验证MLX90393是否真正进入WOC模式
- 检查硬件连接

## 🛠️ 硬件检查清单

### MLX90393连接:
- [ ] VDD → 3.3V
- [ ] GND → GND  
- [ ] SDA → PB11 (I2C2_SDA)
- [ ] SCL → PB10 (I2C2_SCL)
- [ ] INT → GPIO (配置为输入，内部上拉)

### STM32 GPIO配置:
- [ ] INT引脚配置为GPIO输入模式
- [ ] 启用内部上拉电阻
- [ ] 可选：配置为外部中断 (EXTI)

### 示波器测试:
1. **测量INT引脚电压**
   - 正常: 3.3V (高电平)
   - 中断: 0V (低电平)

2. **观察波形**
   - 连续模式: 持续方波 (~15Hz)
   - WOC模式: 平时高电平，变化时短暂低电平

## 📊 预期测试结果

### 成功的WOC模式:
```
=== Simple WOC Test ===
Step 1: Check current register status...
Register 0x01: 0x8F40
  TRIG_INT (bit 9): 1
  WOC_DIFF (bit 6): 1
Step 3: Set thresholds...
Thresholds set to 200 LSB
Step 4: Try WOC command...
Trying WOC command 0x20 with axes 0x0E...
WOC command successful! Status: 0x??
Now monitor INT pin for changes...
```

### 中断检测:
```
Monitoring... (0/100)
WOC interrupt detected! Status: 0x1?
Monitoring... (10/100)
```

## 🎯 下一步行动

### 1. 编译并测试新代码
- 使用修正后的WOC命令 (0x20)
- 运行SimpleWOCTest函数
- 观察详细的调试输出

### 2. 硬件验证
- 用示波器监测INT引脚
- 用磁铁测试中断触发
- 确认引脚电平变化

### 3. 根据结果调整
- 如果寄存器写入失败 → 检查写寄存器函数
- 如果WOC命令失败 → 尝试其他命令格式
- 如果INT引脚无变化 → 检查硬件连接

## 💡 调试技巧

### 1. 分步验证
```c
// 单独测试每个步骤
MLX90393_ReadRegister(mlx, 0x01, &reg_data);
MLX90393_WriteRegister(mlx, 0x01, new_value);
MLX90393_ReadRegister(mlx, 0x01, &verify_data);
```

### 2. 状态位解析
```c
printf("Status bits: ERROR=%d, SM=%d, BURST=%d, WOC=%d\r\n",
       (status >> 7) & 1, (status >> 6) & 1,
       (status >> 5) & 1, (status >> 4) & 1);
```

### 3. 命令重试机制
```c
// 如果第一次失败，尝试不同格式
if (hal_result != HAL_OK) {
    // 尝试单字节命令
    // 尝试不同的轴选择
    // 增加延时
}
```

通过这些修正和调试步骤，应该能够解决WOC模式的问题并实现正确的中断功能。
