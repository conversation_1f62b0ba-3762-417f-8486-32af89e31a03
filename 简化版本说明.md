# MLX90393 简化版本说明

## 🎯 **已完成的简化**

### ✅ **删除的调试输出**
- 初始化过程的详细信息
- 寄存器配置的详细输出  
- DEBUG XYZ原始数据显示
- SM命令错误信息
- WOC配置过程信息

### ✅ **保留的核心功能**
- 三轴磁场数据输出: `X:  24.5 Y: 242.6 Z:104.1`
- WOC中断检测: `*** MAGNETIC FIELD CHANGE DETECTED! ***`
- WOC中断数据: `X:  25.3 Y: 285.6 Z:156.7 [WOC]`
- LED状态指示

## 🔧 **当前配置**

### **WOC参数** (在mlx90393.h中可调整)
```c
#define MLX90393_WOC_ENABLE         1       // WOC功能启用
#define MLX90393_WOC_SENSITIVITY_LOW        // 低灵敏度 (500 LSB)
#define MLX90393_WOC_AXES  (X | Y | Z)      // 监测所有轴
```

### **灵敏度选项**
- `MLX90393_WOC_SENSITIVITY_HIGH` - 50 LSB (高灵敏度)
- `MLX90393_WOC_SENSITIVITY_MEDIUM` - 200 LSB (中等灵敏度)  
- `MLX90393_WOC_SENSITIVITY_LOW` - 500 LSB (低灵敏度) ← 当前

### **监测轴选择**
```c
// 可选组合
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z                    // 只监测Z轴
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y)  // X+Y轴
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)  // 全部轴 ← 当前
```

## 📊 **程序输出**

### **正常运行时**
```
X:  24.5 Y: 242.6 Z:104.1
X:  11.7 Y: 251.6 Z:113.7
X:   6.4 Y: 253.8 Z:113.7
```

### **WOC中断触发时**
```
*** MAGNETIC FIELD CHANGE DETECTED! ***
X:  25.3 Y: 285.6 Z:156.7 [WOC]
X:  11.7 Y: 251.6 Z:113.7
```

## 🔌 **INT引脚行为**

### **期望的正常行为**
- **初始状态**: 低电平 (0V)
- **磁场变化**: 高电平 (3.3V) 
- **读取数据后**: 返回低电平

### **如果还是15Hz方波**
说明WOC模式启动失败，传感器仍在连续测量模式。

## 🛠️ **故障排除**

### **如果INT还是15Hz方波**
1. **检查WOC命令**: 确认0x20命令发送成功
2. **检查寄存器**: TRIG_INT和WOC_DIFF位是否设置
3. **尝试不同灵敏度**: 改为HIGH或MEDIUM
4. **检查轴选择**: 尝试只监测Z轴

### **调整灵敏度测试**
```c
// 在mlx90393.h中修改
#define MLX90393_WOC_SENSITIVITY_HIGH    // 改为高灵敏度测试
```

### **简化轴选择测试**
```c
// 只监测Z轴
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z
```

## 🎯 **下一步测试**

1. **编译新代码** - 应该没有调试输出了
2. **观察INT引脚** - 用示波器检查是否还是15Hz
3. **测试磁铁** - 靠近传感器看是否触发中断
4. **调整参数** - 如果不工作，尝试不同的灵敏度和轴选择

## 📋 **快速配置修改**

### **提高灵敏度** (如果不够敏感)
```c
// 在 Inc/mlx90393.h 中
//#define MLX90393_WOC_SENSITIVITY_LOW     // 注释掉
#define MLX90393_WOC_SENSITIVITY_HIGH      // 启用高灵敏度
```

### **只监测Z轴** (简化测试)
```c
// 在 Inc/mlx90393.h 中
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z  // 只监测Z轴
```

### **禁用WOC** (回到纯连续模式)
```c
// 在 Inc/mlx90393.h 中
#define MLX90393_WOC_ENABLE         0       // 禁用WOC
```

现在代码已经大幅简化，只保留核心功能和数据输出。如果INT引脚还是15Hz，请尝试调整上述参数进行测试。
