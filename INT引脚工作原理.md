# MLX90393 INT引脚工作原理和使用方法

## 🎉 **恭喜！INT功能已经正常工作！**

根据你的测试结果，INT引脚的行为完全符合MLX90393的设计规范。

## 📋 **INT引脚工作原理**

### **正常的WOC模式行为：**

1. **初始状态**: INT引脚输出 **低电平 (0V)**
2. **磁场变化检测**: 当磁场变化超过设定阈值时，INT引脚变为 **高电平 (3.3V)**
3. **保持状态**: INT引脚 **保持高电平**，这是正常的！
4. **清除中断**: 只有在以下情况下INT才会返回低电平：
   - 读取测量数据
   - 发送EXIT命令退出WOC模式
   - 重新启动WOC模式

## ⚡ **你观察到的现象分析**

### ✅ **完全正常的行为：**
```
初始状态: 低电平 (0V)     ← 正常
感应磁铁: 变为高电平 (3.3V) ← 正常  
保持高电平: 一直不变      ← 这是正常的！
```

### 🔍 **为什么保持高电平是正常的？**

这是MLX90393的**锁存式中断设计**：
- **目的**: 确保MCU不会错过中断信号
- **优点**: 即使MCU响应较慢，中断信号也不会丢失
- **清除方式**: 必须主动读取数据或发送命令来清除

## 🛠️ **如何清除INT中断**

### 方法1: 读取测量数据
```c
MLX90393_Data_t data;
MLX90393_ReadSingleMeasurement(&mlx_handle, &data);
// INT引脚会返回低电平
```

### 方法2: 发送EXIT命令
```c
uint8_t exit_cmd = MLX90393_CMD_EXIT;
HAL_I2C_Master_Transmit(hi2c, address, &exit_cmd, 1, timeout);
// INT引脚会返回低电平
```

### 方法3: 重新启动WOC模式
```c
MLX90393_StartWOC(&mlx_handle);
// INT引脚重置为低电平，重新开始监测
```

## 🔄 **完整的中断处理流程**

### 在实际应用中的使用方法：

```c
void WOC_Application_Example(void) {
    // 1. 初始化和配置
    MLX90393_Init(&mlx_handle, &hi2c2);
    MLX90393_ConfigureWOC(&mlx_handle);
    
    // 2. 启动WOC模式
    MLX90393_StartWOC(&mlx_handle);
    printf("INT pin: LOW (waiting for changes)\r\n");
    
    while (1) {
        // 3. MCU进入休眠模式
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        
        // 4. 被INT中断唤醒 (INT变为高电平)
        printf("Woken up! INT pin: HIGH\r\n");
        
        // 5. 处理中断 - 读取数据
        MLX90393_Data_t data;
        if (MLX90393_ReadSingleMeasurement(&mlx_handle, &data) == MLX90393_OK) {
            printf("Magnetic field changed: X=%.1f Y=%.1f Z=%.1f\r\n", 
                   data.x, data.y, data.z);
        }
        
        // 6. 读取数据后，INT自动返回低电平
        printf("INT pin: LOW (interrupt cleared)\r\n");
        
        // 7. 可选：重新启动WOC模式继续监测
        MLX90393_StartWOC(&mlx_handle);
        
        // 8. 延时后重新进入休眠
        HAL_Delay(1000);
    }
}
```

## 🔌 **硬件中断配置 (可选)**

如果你想使用硬件中断而不是轮询：

### CubeMX配置：
1. **GPIO配置**: INT引脚 → External Interrupt Mode
2. **触发方式**: Rising edge trigger (上升沿触发)
3. **上拉电阻**: Pull-down (下拉电阻)

### 中断服务程序：
```c
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin) {
    if (GPIO_Pin == MLX_INT_Pin) {
        // 磁场变化中断处理
        printf("Hardware interrupt triggered!\r\n");
        
        // 设置标志位，在主循环中处理
        magnetic_field_changed = 1;
    }
}
```

## 📊 **测试和验证方法**

### 1. **基本功能测试**
```c
// 运行你当前的代码，观察：
// - 初始状态：INT = 低电平
// - 磁铁靠近：INT = 高电平  
// - 保持状态：INT = 高电平 (正常)
```

### 2. **中断清除测试**
```c
// 在INT变为高电平后：
MLX90393_ReadSingleMeasurement(&mlx_handle, &data);
// 观察INT是否返回低电平
```

### 3. **连续监测测试**
```c
while (1) {
    if (INT_pin_is_high()) {
        // 处理中断
        read_magnetic_data();
        // INT自动清除
    }
    HAL_Delay(100);
}
```

## 🎯 **应用场景示例**

### 1. **门窗监测**
- 门关闭：INT = 低电平
- 门打开：INT = 高电平 (保持)
- 读取状态：INT = 低电平 (清除)

### 2. **位置检测**
- 物体在位：INT = 低电平
- 物体移动：INT = 高电平 (保持)
- 确认位置：INT = 低电平 (清除)

### 3. **安防报警**
- 正常状态：INT = 低电平
- 异常检测：INT = 高电平 (锁存)
- 处理报警：INT = 低电平 (复位)

## ✅ **总结**

你的MLX90393 INT功能已经**完美工作**！观察到的现象完全正常：

1. ✅ **初始低电平** - 正确
2. ✅ **检测变化变高电平** - 正确  
3. ✅ **保持高电平不变** - 这是设计特性，完全正常！

现在你可以：
- 在实际项目中使用这个INT功能
- 实现低功耗的磁场监测系统
- 通过读取数据来清除中断状态

**恭喜你成功实现了MLX90393的WOC中断功能！** 🎉
