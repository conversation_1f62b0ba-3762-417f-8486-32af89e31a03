# MLX90393 WOC模式硬件连接说明

## 🔌 基本I2C连接

### MLX90393 → STM32L496
```
VDD  → 3.3V
GND  → GND
SDA  → PB11 (I2C2_SDA)
SCL  → PB10 (I2C2_SCL)
INT  → 任意GPIO (配置为外部中断)
```

## ⚡ INT引脚中断配置

### 1. 硬件连接
```
MLX90393 INT引脚 → STM32 GPIO引脚
```

**推荐连接:**
- INT → PA0 (EXTI0)
- INT → PC13 (EXTI13) 
- 或任意可配置为EXTI的GPIO

### 2. CubeMX配置步骤

#### GPIO配置:
1. 选择INT连接的GPIO引脚
2. 模式设置为: `External Interrupt Mode with Falling edge trigger detection`
3. GPIO上拉/下拉: `Pull-up` (启用内部上拉)
4. 用户标签: `MLX_INT`

#### NVIC配置:
1. 启用对应的EXTI中断: `EXTIx interrupt`
2. 设置中断优先级 (建议: Preemption Priority = 1)

### 3. 代码实现

#### 中断服务程序:
```c
// 在main.c中添加
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    if (GPIO_Pin == MLX_INT_Pin) {
        // WOC中断处理
        printf("WOC interrupt triggered!\r\n");
        
        // 检查中断状态
        MLX90393_CheckWOCStatus(&mlx_handle);
        
        // 可选: 设置标志位供主循环处理
        // woc_interrupt_flag = 1;
    }
}
```

## 🔋 低功耗应用示例

### 完整的休眠唤醒流程:
```c
void LowPowerWOCApplication(void)
{
    // 1. 初始化MLX90393
    MLX90393_Init(&mlx_handle, &hi2c2);
    
    // 2. 配置并启动WOC模式
    MLX90393_ConfigureWOC(&mlx_handle);
    MLX90393_StartWOC(&mlx_handle);
    
    printf("Entering low power mode...\r\n");
    
    while (1) {
        // 3. 进入STOP模式 (最低功耗)
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        
        // 4. 被INT中断唤醒后执行
        printf("Woken up by magnetic field change!\r\n");
        
        // 5. 系统时钟恢复
        SystemClock_Config();
        
        // 6. 处理磁场数据
        MLX90393_CheckWOCStatus(&mlx_handle);
        
        // 7. 可选: 重新启动WOC模式继续监测
        // MLX90393_StartWOC(&mlx_handle);
        
        // 8. 延时后重新进入休眠
        HAL_Delay(1000);
    }
}
```

## 📊 功耗分析

### 正常工作模式:
- STM32L496 运行: ~1.5mA @ 3.3V
- MLX90393 连续测量: ~1.5mA @ 3.3V
- **总功耗: ~3mA**

### WOC低功耗模式:
- STM32L496 STOP模式: ~2μA @ 3.3V
- MLX90393 WOC模式: ~15μA @ 3.3V
- **总功耗: ~17μA**

### 功耗优化效果:
- **功耗降低: 176倍** (3mA → 17μA)
- **电池寿命延长: 176倍**

## 🛠️ 调试技巧

### 1. 验证INT引脚连接
```c
// 测试INT引脚状态
uint8_t int_state = HAL_GPIO_ReadPin(MLX_INT_GPIO_Port, MLX_INT_Pin);
printf("INT pin state: %d\r\n", int_state);
```

### 2. 测试中断触发
- 用万用表测量INT引脚电压
- 正常状态: 3.3V (高电平)
- 中断触发: 0V (低电平)

### 3. 磁场测试
- 使用强磁铁靠近传感器
- 观察串口输出的中断信息
- 检查磁场数值变化

## ⚠️ 注意事项

### 1. 上拉电阻
- INT引脚需要上拉电阻
- 可使用STM32内部上拉 (推荐)
- 或外接4.7kΩ上拉电阻

### 2. 中断触发方式
- 必须配置为**下降沿触发**
- INT引脚在检测到变化时输出低电平

### 3. 阈值调整
- 根据应用场景调整WOC阈值
- 阈值过小: 容易误触发
- 阈值过大: 可能错过变化

### 4. 环境干扰
- 避免强磁场干扰源
- 考虑温度对磁场的影响
- 建议进行现场校准

## 🎯 应用场景

### 1. 门窗监测
- 门/窗上安装磁铁
- 传感器安装在门框/窗框
- 开关动作触发WOC中断

### 2. 位置检测
- 监测物体位置变化
- 磁铁标记目标物体
- 位置改变时触发报警

### 3. 安防系统
- 检测金属物体接近
- 磁场异常报警
- 低功耗长期监测

### 4. IoT传感器
- 电池供电设备
- 定期上报磁场变化
- 超长待机时间

## 📈 性能指标

- **响应时间**: < 10ms
- **检测精度**: 可配置 (50-500 LSB)
- **工作温度**: -40°C 到 +85°C
- **供电电压**: 2.2V 到 3.6V
- **I2C速度**: 最高400kHz
- **功耗**: WOC模式 ~15μA

通过合理的硬件连接和软件配置，MLX90393的WOC功能可以实现高效、可靠的低功耗磁场监测系统。
